#!/usr/bin/env python3
"""
Test script to verify that text generation works properly after fixing the causal attention issue.
"""

import torch
from transformers import AutoProcessor
from models.qwen2_vl import Qwen2VLRetForConditionalGeneration

def test_text_generation():
    """Test text generation with proper causal attention."""
    
    # Load model and processor
    model_path = "./checkpoints/qwen2_5-vl-7b_DAM_pretrain_vision"
    
    try:
        model = Qwen2VLRetForConditionalGeneration.from_pretrained(
            model_path, 
            attn_implementation="flash_attention_2", 
            torch_dtype=torch.bfloat16, 
            low_cpu_mem_usage=True
        ).cuda()
        
        processor = AutoProcessor.from_pretrained("Qwen/Qwen2.5-VL-7B-Instruct")
        tokenizer = processor.tokenizer
        
        # Add embed token
        emb_tokens = ["<emb>"]
        num_new_tokens = tokenizer.add_tokens(emb_tokens)
        if num_new_tokens > 0:
            model.resize_token_embeddings(len(tokenizer))
        emb_token_ids = tokenizer.convert_tokens_to_ids(emb_tokens)
        model.config.emb_token_ids = emb_token_ids
        
        # CRITICAL: Ensure causal attention is enabled for proper text generation
        model.config.nocausal_attn = False
        print(f"Model nocausal_attn setting: {model.config.nocausal_attn}")
        
        # Test simple text generation
        test_messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Please write a short story about a cat."}
                ],
            }
        ]
        
        # Process input
        text = processor.apply_chat_template(test_messages, tokenize=False, add_generation_prompt=True)
        inputs = processor(text=[text], return_tensors="pt").to("cuda")
        
        print("Input text:", text)
        print("Generating text...")
        
        # Generate text
        with torch.no_grad():
            output = model.generate(
                **inputs, 
                max_new_tokens=200,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=tokenizer.eos_token_id
            )
            
            # Decode output
            input_length = inputs['input_ids'].shape[1]
            generated_tokens = output[:, input_length:]
            generated_text = tokenizer.batch_decode(generated_tokens, skip_special_tokens=True)[0]
            
            print("Generated text:")
            print(generated_text)
            print("\n" + "="*50)
            
            # Check if generation looks reasonable
            if len(generated_text.strip()) > 10 and not is_repetitive(generated_text):
                print("✅ Text generation appears to be working correctly!")
                return True
            else:
                print("❌ Text generation still appears problematic.")
                return False
                
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def is_repetitive(text, threshold=0.7):
    """Check if text is overly repetitive."""
    words = text.split()
    if len(words) < 10:
        return False
    
    # Check for repeated phrases
    word_counts = {}
    for word in words:
        word_counts[word] = word_counts.get(word, 0) + 1
    
    # If any word appears more than threshold of total words, it's repetitive
    max_count = max(word_counts.values())
    return max_count / len(words) > threshold

if __name__ == "__main__":
    print("Testing text generation after causal attention fix...")
    success = test_text_generation()
    
    if success:
        print("\n🎉 Fix appears to be successful!")
    else:
        print("\n⚠️  Additional investigation may be needed.")
