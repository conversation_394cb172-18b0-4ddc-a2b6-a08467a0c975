from typing import List, <PERSON><PERSON>
import datasets
from datasets import load_dataset, concatenate_datasets
import torch
from torch.utils.data import Dataset
from PIL import Image
import os

vlm_image_tokens = {
    "PHI3V": "<|image_1|>",
    "LLAVA_NEXT": "<image>",
    "QWEN2_VL": "<|image_pad|>",
    "QWEN2_5_VL": "<|image_pad|>",
}


def process_image(image, resolution, max_dim=1344):
    if image is None:
        return None
    if resolution == "high":
        image = image.resize((1344, 1344))
    elif resolution == "mid":
        image = image.resize((672, 672))
    elif resolution == "low":
        image = image.resize((128, 128))
    else:
        cur_max_dim = max(image.size)
        if cur_max_dim > max_dim:
            image = image.resize((max_dim, max_dim))
    return image


class MMEBEvalDataset(Dataset):
    def __init__(self, data_args, subset, text_field, img_path_field):
        """
        (text_field, image_field) -> ("qry_text", "qry_img_path") or ("tgt_text", "tgt_img_path")
        """
        super().__init__()

        self.dataset_name = data_args["dataset_name"]
        self.image_resolution = data_args["image_resolution"]
        self.image_dir = data_args["image_dir"]

        self.eval_data = load_dataset(
            self.dataset_name, # TIGER-Lab/MMEB-eval
            subset, # subset name
            split="test",
        )
        self.paired_data = self.get_paired_data(text_field, img_path_field)
        self.paired_dataset = datasets.Dataset.from_dict({
            "text": [pair["text"] for pair in self.paired_data],
            "img_path": [pair["img_path"] for pair in self.paired_data]
        })

    def __len__(self):
        return len(self.paired_dataset)

    def __getitem__(self, item):
        text, img_path = self.paired_dataset[item]["text"], self.paired_dataset[item]["img_path"]
        text = text.replace(vlm_image_tokens["PHI3V"], "") #NOTE in mbeir there are no such things vlm_image_tokens["QWEN2_5_VL"]
        if img_path != "" and img_path is not None:
            img_path = os.path.join(self.image_dir, img_path)
        message = self.construct_messages(self._prepare_data_dict(text, img_path))

        return message, None

    def _prepare_data_dict(self, txt, img_path, box=None):
        if img_path is None or img_path == '':
            return {'txt': txt, "box":box}
        elif txt == '':
            return {'image': img_path, "box":box}
        return {"txt": txt, "image": img_path, "box":box}

    def construct_messages(self, data_dict):
        if 'txt' in data_dict and 'image' in data_dict:
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": data_dict['image'], "box": data_dict["box"]},
                        {"type": "text", "text": f"{data_dict['txt']}\nSummarize above image and sentence in one word: "}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": f"<emb>."}
                    ]
                },
            ]
        elif 'txt' in data_dict:
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": f"{data_dict['txt']}\nSummarize above sentence in one word: "}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": f"<emb>."}
                    ]
                },
            ]
        elif 'image' in data_dict:
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": data_dict['image'], "box": data_dict["box"]},
                        {"type": "text", "text": f"\nSummarize above image in one word: "}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": f"<emb>."}
                    ]
                },
            ]
        return message

    def get_paired_data(self, text_field, img_path_field):
        """
        (text_field, image_field) -> ("qry_text", "qry_img_path") or ("tgt_text", "tgt_img_path")
        """
        unique_pair = set()
        for row in self.eval_data:
            if isinstance(row[text_field], str):
                if row[text_field]:
                    unique_pair.add((row[text_field], row[img_path_field]))
                else:
                    if isinstance(row[img_path_field], List):
                        for img_path in row[img_path_field]:
                            unique_pair.add((row[text_field], img_path))
                    else:
                        unique_pair.add((row[text_field], row[img_path_field]))
            elif type(row[text_field]) == list:
                assert type(row[img_path_field]) == list and len(row[img_path_field]) == len(row[text_field])
                for text, img_path in zip(row[text_field], row[img_path_field]):
                    unique_pair.add((text, img_path))

        paired_data = [{"text": text, "img_path": img_path} for text, img_path in unique_pair]
        return paired_data


class MMEBTrainDataset(Dataset):
    def __init__(self, data_args):
        """
        MMEB训练数据集
        data_args 应该包含:
        - dataset_name: 数据集名称
        - subset_name: 子集名称列表
        - split_name: 分割名称 (如 'train')
        - image_dir: 图片目录
        - image_resolution: 图片分辨率
        """
        super().__init__()

        self.dataset_name = data_args["dataset_name"]
        self.subset_name = data_args["subset_name"]
        self.split_name = data_args["split_name"]
        self.image_dir = data_args["image_dir"]
        self.image_resolution = data_args.get("image_resolution", None)

        train_data = []
        print(f"Loading {len(self.subset_name)} datasets: {self.subset_name}")
        for subset in self.subset_name:
            subset_data = load_dataset(self.dataset_name, subset, split=self.split_name)
            train_data.append(subset_data)
        self.train_data = concatenate_datasets(train_data)

    def __len__(self):
        return len(self.train_data)

    def _get_image(self, img_path):
        if not img_path:
            return None
        full_img_path = os.path.join(self.image_dir, img_path)
        image = Image.open(full_img_path)
        if self.image_resolution:
            return process_image(image, self.image_resolution)
        else:
            return image

    def _prepare_data_dict(self, txt, img_path, box=None):
        if img_path is None or img_path == '':
            return {'txt': txt, "box": box}
        elif txt == '':
            return {'image': img_path, "box": box}
        return {"txt": txt, "image": img_path, "box": box}

    def construct_messages(self, data_dict):
        if 'txt' in data_dict and 'image' in data_dict:
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": data_dict['image'], "box": data_dict["box"]},
                        {"type": "text", "text": f"{data_dict['txt']}\nSummarize above image and sentence in one word: "}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": f"<emb>."}
                    ]
                },
            ]
        elif 'txt' in data_dict:
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": f"{data_dict['txt']}\nSummarize above sentence in one word: "}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": f"<emb>."}
                    ]
                },
            ]
        elif 'image' in data_dict:
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": data_dict['image'], "box": data_dict["box"]},
                        {"type": "text", "text": f"\nSummarize above image in one word: "}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": f"<emb>."}
                    ]
                },
            ]
        return message

    def __getitem__(self, data_idx):
        qry_texts, qry_image_paths, pos_texts, pos_image_paths = (
            self.train_data[data_idx]["qry"], self.train_data[data_idx]["qry_image_path"],
            self.train_data[data_idx]["pos_text"], self.train_data[data_idx]["pos_image_path"]
        )

        # 检查是否有负样本
        if 'neg_text' in self.train_data.column_names:
            neg_texts, neg_image_paths = self.train_data[data_idx]["neg_text"], self.train_data[data_idx]["neg_image_path"]
        else:
            neg_texts, neg_image_paths = [''] * len(data_idx) if not isinstance(data_idx, int) else [''], [] * len(data_idx) if not isinstance(data_idx, int) else []

        # 确保数据是列表格式
        if isinstance(data_idx, int):
            qry_texts = [qry_texts]
            qry_image_paths = [qry_image_paths]
            pos_texts = [pos_texts]
            pos_image_paths = [pos_image_paths]
            neg_texts = [neg_texts]
            neg_image_paths = [neg_image_paths]

        _qry_messages, _pos_messages, _neg_messages = [], [], []

        for qry_text, qry_image_path, pos_text, pos_image_path, neg_text, neg_image_path \
            in zip(qry_texts, qry_image_paths, pos_texts, pos_image_paths, neg_texts, neg_image_paths):

            # 移除 PHI3V 的图像标记
            qry_text = qry_text.replace(vlm_image_tokens["PHI3V"], "") if qry_text else ""
            pos_text = pos_text.replace(vlm_image_tokens["PHI3V"], "") if pos_text else ""
            neg_text = neg_text.replace(vlm_image_tokens["PHI3V"], "") if neg_text else ""

            # 处理图像路径
            qry_image_full_path = os.path.join(self.image_dir, qry_image_path) if qry_image_path else None
            pos_image_full_path = os.path.join(self.image_dir, pos_image_path) if pos_image_path else None
            neg_image_full_path = os.path.join(self.image_dir, neg_image_path) if neg_image_path else None

            # 检查是否有有效的输入
            if (not qry_text and not qry_image_path) or (not pos_text and not pos_image_path):
                print("empty inputs")
                continue

            # 构建查询消息
            qry_data_dict = self._prepare_data_dict(qry_text, qry_image_full_path)
            qry_message = self.construct_messages(qry_data_dict)
            _qry_messages.append(qry_message)

            # 构建正样本消息
            pos_data_dict = self._prepare_data_dict(pos_text, pos_image_full_path)
            pos_message = self.construct_messages(pos_data_dict)
            _pos_messages.append(pos_message)

            # 构建负样本消息（如果存在）
            if neg_text or neg_image_path:
                neg_data_dict = self._prepare_data_dict(neg_text, neg_image_full_path)
                neg_message = self.construct_messages(neg_data_dict)
                _neg_messages.append(neg_message)
            else:
                _neg_messages.append(None)

        return {"query_messages": _qry_messages, "pos_messages": _pos_messages, "neg_messages": _neg_messages}


if __name__ == "__main__":
    # 测试 EvalDataset
    eval_data_args = {
        "dataset_name": "/mnt/tidal-alsh01/dataset/mmeb/mmeb-eval/",
        "image_resolution": "low",
        "image_dir": "/mnt/tidal-alsh01/dataset/mmeb/mmeb-eval/"
    }

    eval_ds = MMEBEvalDataset(
            data_args=eval_data_args,
            subset="MSCOCO_t2i",
            text_field="qry_text",
            img_path_field="qry_img_path",
        )

    print(f"EvalDataset length: {len(eval_ds)}")
    print(f"EvalDataset sample: {eval_ds[0]}")

    # 测试 TrainDataset
    train_data_args = {
        "dataset_name": "/mnt/tidal-alsh01/dataset/mmeb/mmeb-train/",  # 假设的训练数据路径
        "subset_name": ["MSCOCO_t2i"],  # 子集列表
        "split_name": "train",
        "image_dir": "/mnt/tidal-alsh01/dataset/mmeb/mmeb-train/",
        "image_resolution": "low"
    }

    try:
        train_ds = MMEBTrainDataset(data_args=train_data_args)
        print(f"TrainDataset length: {len(train_ds)}")
        print(f"TrainDataset sample: {train_ds[0]}")
    except Exception as e:
        print(f"TrainDataset test failed: {e}")
        print("This is expected if the training data path doesn't exist")