# get zeroshot rerank results 
# python eval/rerank/zeroshot_rerank/eval_t2i_and_i2t.py --task_name urban1k_t2i
# python eval/rerank/zeroshot_rerank/eval_t2i_and_i2t.py --task_name urban1k_i2t
# python eval/rerank/zeroshot_rerank/eval_t2i_and_i2t.py --task_name sharegpt4v_t2i
# python eval/rerank/zeroshot_rerank/eval_t2i_and_i2t.py --task_name sharegpt4v_i2t
# python eval/rerank/zeroshot_rerank/eval_t2i_and_i2t.py --task_name flickr_t2i
# python eval/rerank/zeroshot_rerank/eval_t2i_and_i2t.py --task_name flickr_i2t
# python eval/rerank/zeroshot_rerank/eval_circo.py  # get circo test submission file for evaluation
python eval/rerank/zeroshot_rerank/eval_genecis.py --task_name genecis_change_attribute
python eval/rerank/zeroshot_rerank/eval_genecis.py --task_name genecis_focus_attribute
python eval/rerank/zeroshot_rerank/eval_genecis.py --task_name genecis_change_object
python eval/rerank/zeroshot_rerank/eval_genecis.py --task_name genecis_focus_object
# python eval/rerank/zeroshot_rerank/eval_visdial.py
# python eval/rerank/zeroshot_rerank/eval_vist.py
# python eval/rerank/zeroshot_rerank/eval_mrf.py
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name ccneg
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type add_att
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type add_obj
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type replace_att
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type replace_obj
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type replace_rel
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type swap_att
# python eval/rerank/zeroshot_rerank/eval_itm.py --task_name sugar_crepe --data_type swap_obj
# python eval/rerank/zeroshot_rerank/eval_visdial.py
# python eval/rerank/zeroshot_rerank/eval_vist.py
# python eval/rerank/zeroshot_rerank/eval_mrf.py
